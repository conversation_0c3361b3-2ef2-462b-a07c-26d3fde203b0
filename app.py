from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from flask_babel import Babel, gettext, ngettext
from werkzeug.utils import secure_filename
import os
from datetime import datetime
import uuid
import qrcode
from barcode import Code128
from barcode.writer import ImageWriter
import io
import base64
from PIL import Image

from config import Config
from models import *
from utils import *

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # Initialize extensions
    db.init_app(app)
    
    # Login manager
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # Babel for internationalization
    babel = Babel(app)
    
    @babel.localeselector
    def get_locale():
        # Check if language is set in session
        if 'language' in session:
            return session['language']
        # Check if language is in request args
        if request.args.get('lang'):
            session['language'] = request.args.get('lang')
            return session['language']
        # Default to Arabic
        return 'ar'
    
    # Create upload directory
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    
    # Register blueprints
    from routes.auth import auth_bp
    from routes.dashboard import dashboard_bp
    from routes.products import products_bp
    from routes.inventory import inventory_bp
    from routes.invoices import invoices_bp
    from routes.customers import customers_bp
    from routes.suppliers import suppliers_bp
    from routes.reports import reports_bp
    from routes.settings import settings_bp
    from routes.pos import pos_bp
    
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(dashboard_bp, url_prefix='/dashboard')
    app.register_blueprint(products_bp, url_prefix='/products')
    app.register_blueprint(inventory_bp, url_prefix='/inventory')
    app.register_blueprint(invoices_bp, url_prefix='/invoices')
    app.register_blueprint(customers_bp, url_prefix='/customers')
    app.register_blueprint(suppliers_bp, url_prefix='/suppliers')
    app.register_blueprint(reports_bp, url_prefix='/reports')
    app.register_blueprint(settings_bp, url_prefix='/settings')
    app.register_blueprint(pos_bp, url_prefix='/pos')
    
    # Main routes
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard.index'))
        return redirect(url_for('auth.login'))
    
    @app.route('/set_language/<language>')
    def set_language(language):
        if language in app.config['LANGUAGES']:
            session['language'] = language
        return redirect(request.referrer or url_for('index'))
    
    # Template filters
    @app.template_filter('currency')
    def currency_filter(amount):
        if amount is None:
            amount = 0
        return f"{amount:,.2f} ر.س"
    
    @app.template_filter('datetime')
    def datetime_filter(dt):
        if dt is None:
            return ''
        return dt.strftime('%Y-%m-%d %H:%M')
    
    @app.template_filter('date')
    def date_filter(dt):
        if dt is None:
            return ''
        return dt.strftime('%Y-%m-%d')
    
    # Context processors
    @app.context_processor
    def inject_conf_vars():
        return {
            'COMPANY_NAME': app.config['COMPANY_NAME'],
            'COMPANY_NAME_EN': app.config['COMPANY_NAME_EN'],
            'current_language': session.get('language', 'ar')
        }
    
    # Error handlers
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('errors/500.html'), 500
    
    # Initialize database
    with app.app_context():
        db.create_all()
        init_default_data()
    
    return app

def init_default_data():
    """Initialize default data if database is empty"""
    
    # Create default admin user
    if not User.query.first():
        admin = User(
            username='admin',
            email='<EMAIL>',
            role='admin'
        )
        admin.set_password('admin123')
        db.session.add(admin)
    
    # Create default categories
    if not Category.query.first():
        categories = [
            {'name_ar': 'أجبان', 'name_en': 'Cheese'},
            {'name_ar': 'لحوم', 'name_en': 'Meat'},
            {'name_ar': 'عصائر', 'name_en': 'Juices'},
            {'name_ar': 'خضروات', 'name_en': 'Vegetables'},
            {'name_ar': 'فواكه', 'name_en': 'Fruits'},
        ]
        for cat_data in categories:
            category = Category(**cat_data)
            db.session.add(category)
    
    # Create default units
    if not Unit.query.first():
        units = [
            {'name_ar': 'كيلو', 'name_en': 'Kilogram', 'symbol': 'كجم'},
            {'name_ar': 'قطعة', 'name_en': 'Piece', 'symbol': 'قطعة'},
            {'name_ar': 'علبة', 'name_en': 'Box', 'symbol': 'علبة'},
            {'name_ar': 'لتر', 'name_en': 'Liter', 'symbol': 'لتر'},
            {'name_ar': 'متر', 'name_en': 'Meter', 'symbol': 'متر'},
        ]
        for unit_data in units:
            unit = Unit(**unit_data)
            db.session.add(unit)
    
    # Create default stock statuses
    if not StockStatus.query.first():
        statuses = [
            {'name_ar': 'متوفر', 'name_en': 'Available', 'color': '#28a745'},
            {'name_ar': 'قليل', 'name_en': 'Low Stock', 'color': '#ffc107'},
            {'name_ar': 'نفد', 'name_en': 'Out of Stock', 'color': '#dc3545'},
            {'name_ar': 'متوقف', 'name_en': 'Discontinued', 'color': '#6c757d'},
        ]
        for status_data in statuses:
            status = StockStatus(**status_data)
            db.session.add(status)
    
    # Create default settings
    if not Settings.query.first():
        settings = [
            {'key': 'company_name_ar', 'value': 'شركة المحاسبة', 'description': 'اسم الشركة بالعربية'},
            {'key': 'company_name_en', 'value': 'Accounting Company', 'description': 'Company name in English'},
            {'key': 'tax_rate', 'value': '0.15', 'description': 'Tax rate (VAT)'},
            {'key': 'currency_symbol', 'value': 'ر.س', 'description': 'Currency symbol'},
            {'key': 'low_stock_threshold', 'value': '10', 'description': 'Low stock alert threshold'},
            {'key': 'invoice_prefix', 'value': 'INV', 'description': 'Invoice number prefix'},
            {'key': 'receipt_footer', 'value': 'شكراً لزيارتكم', 'description': 'Receipt footer message'},
        ]
        for setting_data in settings:
            setting = Settings(**setting_data)
            db.session.add(setting)
    
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        print(f"Error initializing default data: {e}")

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
