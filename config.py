import os
from datetime import timedelta

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here-change-in-production'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///accounting.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Upload settings
    UPLOAD_FOLDER = 'static/uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    
    # Session settings
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)
    
    # Language settings
    LANGUAGES = ['ar', 'en']
    BABEL_DEFAULT_LOCALE = 'ar'
    BABEL_DEFAULT_TIMEZONE = 'UTC'
    
    # Business settings
    COMPANY_NAME = 'شركة المحاسبة'
    COMPANY_NAME_EN = 'Accounting Company'
    TAX_RATE = 0.15  # 15% VAT
    
    # Thermal printer settings
    PRINTER_INTERFACE = 'usb'
    PRINTER_VENDOR_ID = 0x04b8
    PRINTER_PRODUCT_ID = 0x0202
