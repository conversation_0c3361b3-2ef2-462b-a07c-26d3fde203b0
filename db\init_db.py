# إعداد قاعدة البيانات والجداول الأساسية
import sqlite3

def init_db():
    conn = sqlite3.connect('db/warehouse.db')
    c = conn.cursor()
    # جدول المستخدمين
    c.execute('''CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        role TEXT NOT NULL,
        full_name TEXT,
        active INTEGER DEFAULT 1
    )''')
    # جدول المنتجات
    c.execute('''CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        unit TEXT NOT NULL,
        quantity INTEGER DEFAULT 0,
        status TEXT,
        category TEXT,
        notes TEXT,
        image_path TEXT
    )''')
    # جدول الموردين
    c.execute('''CREATE TABLE IF NOT EXISTS suppliers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL
    )''')
    # جدول العملاء
    c.execute('''CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL
    )''')
    # جدول فواتير الإدخال
    c.execute('''CREATE TABLE IF NOT EXISTS purchase_invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_number TEXT NOT NULL,
        date TEXT NOT NULL,
        supplier_id INTEGER,
        total REAL,
        FOREIGN KEY(supplier_id) REFERENCES suppliers(id)
    )''')
    # تفاصيل فواتير الإدخال
    c.execute('''CREATE TABLE IF NOT EXISTS purchase_invoice_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER,
        product_id INTEGER,
        quantity INTEGER,
        price REAL,
        total REAL,
        FOREIGN KEY(invoice_id) REFERENCES purchase_invoices(id),
        FOREIGN KEY(product_id) REFERENCES products(id)
    )''')
    # جدول فواتير الإخراج
    c.execute('''CREATE TABLE IF NOT EXISTS sales_invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_number TEXT NOT NULL,
        date TEXT NOT NULL,
        customer_id INTEGER,
        total REAL,
        FOREIGN KEY(customer_id) REFERENCES customers(id)
    )''')
    # تفاصيل فواتير الإخراج
    c.execute('''CREATE TABLE IF NOT EXISTS sales_invoice_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER,
        product_id INTEGER,
        quantity INTEGER,
        price REAL,
        total REAL,
        FOREIGN KEY(invoice_id) REFERENCES sales_invoices(id),
        FOREIGN KEY(product_id) REFERENCES products(id)
    )''')
    # جدول وحدات القياس
    c.execute('''CREATE TABLE IF NOT EXISTS units (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL
    )''')
    # جدول الحالات
    c.execute('''CREATE TABLE IF NOT EXISTS statuses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL
    )''')
    # جدول التصنيفات
    c.execute('''CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL
    )''')
    conn.commit()
    conn.close()

if __name__ == '__main__':
    init_db()
    print('Database initialized.')
