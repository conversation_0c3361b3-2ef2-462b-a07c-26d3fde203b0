import sys
from PyQt6.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QMain<PERSON>indow, QWidget, QVBoxLayout, QLabel, QLineEdit, QPushButton, QMessageBox
from PyQt6.QtCore import Qt
import sqlite3
from config import DARK_STYLE, APP_NAME, LANGUAGE

class LoginWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle(APP_NAME[LANGUAGE])
        self.setStyleSheet(DARK_STYLE)
        self.setFixedSize(350, 250)
        layout = QVBoxLayout()
        self.label = QLabel('تسجيل الدخول' if LANGUAGE == 'ar' else 'Login')
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.user_input = QLineEdit()
        self.user_input.setPlaceholderText('اسم المستخدم' if LANGUAGE == 'ar' else 'Username')
        self.pass_input = QLineEdit()
        self.pass_input.setPlaceholderText('كلمة المرور' if LANGUAGE == 'ar' else 'Password')
        self.pass_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.login_btn = QPushButton('دخول' if LANGUAGE == 'ar' else 'Login')
        self.login_btn.clicked.connect(self.handle_login)
        layout.addWidget(self.label)
        layout.addWidget(self.user_input)
        layout.addWidget(self.pass_input)
        layout.addWidget(self.login_btn)
        self.setLayout(layout)

    def handle_login(self):
        username = self.user_input.text()
        password = self.pass_input.text()
        conn = sqlite3.connect('db/warehouse.db')
        c = conn.cursor()
        c.execute('SELECT id, role, active FROM users WHERE username=? AND password=?', (username, password))
        user = c.fetchone()
        conn.close()
        if user:
            if user[2] == 0:
                QMessageBox.warning(self, 'خطأ', 'المستخدم غير نشط' if LANGUAGE == 'ar' else 'User is inactive')
                return
            self.close()
            from ui.main_window import MainAppWindow
            self.main = MainAppWindow(user[1])
            self.main.show()
        else:
            QMessageBox.warning(self, 'خطأ', 'بيانات الدخول غير صحيحة' if LANGUAGE == 'ar' else 'Invalid credentials')

class MainWindow(QMainWindow):
    def __init__(self, role):
        super().__init__()
        self.setWindowTitle(APP_NAME[LANGUAGE])
        self.setStyleSheet(DARK_STYLE)
        self.setGeometry(100, 100, 1000, 600)
        label = QLabel('مرحباً بك في النظام' if LANGUAGE == 'ar' else 'Welcome to the system')
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setCentralWidget(label)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = LoginWindow()
    window.show()
    sys.exit(app.exec())
