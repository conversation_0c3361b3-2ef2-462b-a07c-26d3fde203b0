import sqlite3
from config import DB_PATH

def get_units():
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('SELECT name FROM units')
    units = [row[0] for row in c.fetchall()]
    conn.close()
    return units

def add_unit(name):
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    try:
        c.execute('INSERT INTO units (name) VALUES (?)', (name,))
        conn.commit()
    except sqlite3.IntegrityError:
        pass
    conn.close()

def get_statuses():
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('SELECT name FROM statuses')
    statuses = [row[0] for row in c.fetchall()]
    conn.close()
    return statuses

def add_status(name):
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    try:
        c.execute('INSERT INTO statuses (name) VALUES (?)', (name,))
        conn.commit()
    except sqlite3.IntegrityError:
        pass
    conn.close()

def get_categories():
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('SELECT name FROM categories')
    categories = [row[0] for row in c.fetchall()]
    conn.close()
    return categories

def add_category(name):
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    try:
        c.execute('INSERT INTO categories (name) VALUES (?)', (name,))
        conn.commit()
    except sqlite3.IntegrityError:
        pass
    conn.close()

def get_products(search=None):
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    if search:
        c.execute('''SELECT code, name, unit, quantity, status, category, notes FROM products WHERE code LIKE ? OR name LIKE ?''', (f'%{search}%', f'%{search}%'))
    else:
        c.execute('''SELECT code, name, unit, quantity, status, category, notes FROM products''')
    products = c.fetchall()
    conn.close()
    return products

def add_product(code, name, unit, quantity, status, category, notes, image_path):
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('''INSERT INTO products (code, name, unit, quantity, status, category, notes, image_path) VALUES (?, ?, ?, ?, ?, ?, ?, ?)''',
              (code, name, unit, quantity, status, category, notes, image_path))
    conn.commit()
    conn.close()

def update_product(code, name, unit, quantity, status, category, notes, image_path):
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('''UPDATE products SET name=?, unit=?, quantity=?, status=?, category=?, notes=?, image_path=? WHERE code=?''',
              (name, unit, quantity, status, category, notes, image_path, code))
    conn.commit()
    conn.close()

def delete_product(code):
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('DELETE FROM products WHERE code=?', (code,))
    conn.commit()
    conn.close()
