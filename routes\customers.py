from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import Customer, Invoice, Payment, db
from sqlalchemy import func

customers_bp = Blueprint('customers', __name__)

@customers_bp.route('/')
@login_required
def index():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    
    query = Customer.query.filter_by(is_active=True)
    
    if search:
        query = query.filter(
            (Customer.name.contains(search)) |
            (Customer.phone.contains(search)) |
            (Customer.email.contains(search))
        )
    
    customers = query.order_by(Customer.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('customers/index.html', 
                         customers=customers,
                         search=search)

@customers_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    if request.method == 'POST':
        try:
            customer = Customer(
                name=request.form.get('name'),
                phone=request.form.get('phone'),
                email=request.form.get('email'),
                address=request.form.get('address'),
                tax_number=request.form.get('tax_number'),
                credit_limit=float(request.form.get('credit_limit', 0))
            )
            
            db.session.add(customer)
            db.session.commit()
            
            flash('تم إضافة العميل بنجاح', 'success')
            return redirect(url_for('customers.index'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')
    
    return render_template('customers/add.html')

@customers_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit(id):
    customer = Customer.query.get_or_404(id)
    
    if request.method == 'POST':
        try:
            customer.name = request.form.get('name')
            customer.phone = request.form.get('phone')
            customer.email = request.form.get('email')
            customer.address = request.form.get('address')
            customer.tax_number = request.form.get('tax_number')
            customer.credit_limit = float(request.form.get('credit_limit', 0))
            
            db.session.commit()
            flash('تم تحديث العميل بنجاح', 'success')
            return redirect(url_for('customers.index'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')
    
    return render_template('customers/edit.html', customer=customer)

@customers_bp.route('/view/<int:id>')
@login_required
def view(id):
    customer = Customer.query.get_or_404(id)
    
    # Get customer invoices
    invoices = Invoice.query.filter_by(customer_id=id).order_by(
        Invoice.created_at.desc()
    ).limit(10).all()
    
    # Get customer payments
    payments = Payment.query.filter_by(customer_id=id).order_by(
        Payment.created_at.desc()
    ).limit(10).all()
    
    # Calculate statistics
    total_invoices = Invoice.query.filter_by(customer_id=id, status='active').count()
    total_sales = db.session.query(func.sum(Invoice.total_amount)).filter_by(
        customer_id=id, status='active'
    ).scalar() or 0
    total_payments = db.session.query(func.sum(Payment.amount)).filter_by(
        customer_id=id, payment_type='payment'
    ).scalar() or 0
    
    return render_template('customers/view.html',
                         customer=customer,
                         invoices=invoices,
                         payments=payments,
                         total_invoices=total_invoices,
                         total_sales=total_sales,
                         total_payments=total_payments)

@customers_bp.route('/delete/<int:id>')
@login_required
def delete(id):
    if current_user.role not in ['admin', 'manager']:
        flash('ليس لديك صلاحية لحذف العملاء', 'error')
        return redirect(url_for('customers.index'))
    
    customer = Customer.query.get_or_404(id)
    
    # Check if customer has invoices
    if Invoice.query.filter_by(customer_id=id).first():
        flash('لا يمكن حذف العميل لوجود فواتير مرتبطة به', 'error')
        return redirect(url_for('customers.index'))
    
    customer.is_active = False
    db.session.commit()
    
    flash('تم حذف العميل بنجاح', 'success')
    return redirect(url_for('customers.index'))

@customers_bp.route('/search')
@login_required
def search():
    query = request.args.get('q', '')
    if len(query) < 2:
        return jsonify([])
    
    customers = Customer.query.filter(
        Customer.is_active == True,
        (Customer.name.contains(query)) |
        (Customer.phone.contains(query))
    ).limit(10).all()
    
    return jsonify([{
        'id': c.id,
        'name': c.name,
        'phone': c.phone,
        'balance': c.current_balance
    } for c in customers])

@customers_bp.route('/add_payment/<int:id>', methods=['POST'])
@login_required
def add_payment(id):
    customer = Customer.query.get_or_404(id)
    
    try:
        amount = float(request.form.get('amount', 0))
        payment_method = request.form.get('payment_method', 'cash')
        notes = request.form.get('notes', '')
        
        if amount <= 0:
            flash('مبلغ الدفع يجب أن يكون أكبر من صفر', 'error')
            return redirect(url_for('customers.view', id=id))
        
        # Create payment record
        payment = Payment(
            customer_id=customer.id,
            amount=amount,
            payment_method=payment_method,
            payment_type='payment',
            notes=notes,
            created_by=current_user.id
        )
        db.session.add(payment)
        
        # Update customer balance
        customer.current_balance -= amount
        
        db.session.commit()
        flash('تم إضافة الدفعة بنجاح', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ: {str(e)}', 'error')
    
    return redirect(url_for('customers.view', id=id))
