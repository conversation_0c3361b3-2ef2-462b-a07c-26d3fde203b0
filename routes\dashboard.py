from flask import Blueprint, render_template, jsonify
from flask_login import login_required, current_user
from models import Product, Invoice, Customer, StockMovement, CashRegister, InvoiceItem, db
from sqlalchemy import func, desc
from datetime import datetime, timedelta

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/')
@login_required
def index():
    # Get today's date
    today = datetime.now().date()

    # Sales statistics
    today_sales = db.session.query(func.sum(Invoice.total_amount)).filter(
        func.date(Invoice.created_at) == today,
        Invoice.status == 'active'
    ).scalar() or 0

    # Monthly sales
    month_start = today.replace(day=1)
    monthly_sales = db.session.query(func.sum(Invoice.total_amount)).filter(
        Invoice.created_at >= month_start,
        Invoice.status == 'active'
    ).scalar() or 0

    # Product statistics
    total_products = Product.query.filter_by(is_active=True).count()
    low_stock_products = Product.query.filter(
        Product.current_stock <= Product.min_stock_level,
        Product.is_active == True
    ).count()

    # Customer statistics
    total_customers = Customer.query.filter_by(is_active=True).count()

    # Recent invoices
    recent_invoices = Invoice.query.filter_by(status='active').order_by(
        desc(Invoice.created_at)
    ).limit(5).all()

    # Low stock products
    low_stock_list = Product.query.filter(
        Product.current_stock <= Product.min_stock_level,
        Product.is_active == True
    ).limit(10).all()

    # Cash register status
    current_register = CashRegister.query.filter_by(status='open').first()

    return render_template('dashboard/index.html',
                         today_sales=today_sales,
                         monthly_sales=monthly_sales,
                         total_products=total_products,
                         low_stock_products=low_stock_products,
                         total_customers=total_customers,
                         recent_invoices=recent_invoices,
                         low_stock_list=low_stock_list,
                         current_register=current_register)

@dashboard_bp.route('/sales_chart')
@login_required
def sales_chart():
    # Get last 7 days sales data
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=6)

    sales_data = []
    current_date = start_date

    while current_date <= end_date:
        daily_sales = db.session.query(func.sum(Invoice.total_amount)).filter(
            func.date(Invoice.created_at) == current_date,
            Invoice.status == 'active'
        ).scalar() or 0

        sales_data.append({
            'date': current_date.strftime('%Y-%m-%d'),
            'sales': float(daily_sales)
        })
        current_date += timedelta(days=1)

    return jsonify(sales_data)

@dashboard_bp.route('/top_products')
@login_required
def top_products():
    # Get top selling products this month
    month_start = datetime.now().date().replace(day=1)

    top_products = db.session.query(
        Product.name_ar,
        func.sum(InvoiceItem.quantity).label('total_quantity'),
        func.sum(InvoiceItem.total_price).label('total_sales')
    ).join(InvoiceItem).join(Invoice).filter(
        Invoice.created_at >= month_start,
        Invoice.status == 'active'
    ).group_by(Product.id).order_by(
        desc('total_sales')
    ).limit(5).all()

    return jsonify([{
        'name': product.name_ar,
        'quantity': float(product.total_quantity),
        'sales': float(product.total_sales)
    } for product in top_products])
