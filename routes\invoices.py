from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import Invoice, InvoiceItem, Product, Customer, StockMovement, CashTransaction, CashRegister, db
from datetime import datetime
import json

invoices_bp = Blueprint('invoices', __name__)

@invoices_bp.route('/')
@login_required
def index():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    status = request.args.get('status', '')
    
    query = Invoice.query
    
    if search:
        query = query.filter(
            (Invoice.invoice_number.contains(search)) |
            (Invoice.customer_name.contains(search))
        )
    
    if status:
        query = query.filter_by(status=status)
    
    invoices = query.order_by(Invoice.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('invoices/index.html', 
                         invoices=invoices,
                         search=search,
                         selected_status=status)

@invoices_bp.route('/view/<int:id>')
@login_required
def view(id):
    invoice = Invoice.query.get_or_404(id)
    return render_template('invoices/view.html', invoice=invoice)

@invoices_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit(id):
    if current_user.role not in ['admin', 'manager']:
        flash('ليس لديك صلاحية لتعديل الفواتير', 'error')
        return redirect(url_for('invoices.index'))
    
    invoice = Invoice.query.get_or_404(id)
    
    if request.method == 'POST':
        try:
            # Parse items from JSON
            items_data = json.loads(request.form.get('items', '[]'))
            
            if not items_data:
                flash('يجب إضافة منتج واحد على الأقل', 'error')
                return redirect(url_for('invoices.edit', id=id))
            
            # Restore stock for old items
            for item in invoice.items:
                product = Product.query.get(item.product_id)
                if product:
                    product.current_stock += item.quantity
            
            # Clear old items
            InvoiceItem.query.filter_by(invoice_id=invoice.id).delete()
            
            # Update invoice details
            invoice.customer_name = request.form.get('customer_name')
            invoice.discount_percentage = float(request.form.get('discount_percentage', 0))
            invoice.payment_method = request.form.get('payment_method', 'cash')
            invoice.notes = request.form.get('notes', '')
            
            subtotal = 0
            
            # Add new items
            for item_data in items_data:
                product = Product.query.get(item_data['product_id'])
                if not product:
                    continue
                
                quantity = float(item_data['quantity'])
                unit_price = float(item_data['unit_price'])
                item_total = quantity * unit_price
                
                # Check stock availability
                if product.current_stock < quantity:
                    flash(f'المخزون غير كافي للمنتج: {product.name_ar}', 'error')
                    db.session.rollback()
                    return redirect(url_for('invoices.edit', id=id))
                
                # Create invoice item
                invoice_item = InvoiceItem(
                    invoice_id=invoice.id,
                    product_id=product.id,
                    product_name=product.name_ar,
                    product_barcode=product.barcode,
                    unit_price=unit_price,
                    quantity=quantity,
                    total_price=item_total
                )
                db.session.add(invoice_item)
                
                # Update product stock
                product.current_stock -= quantity
                
                # Create stock movement
                movement = StockMovement(
                    product_id=product.id,
                    movement_type='out',
                    quantity=-quantity,
                    previous_stock=product.current_stock + quantity,
                    new_stock=product.current_stock,
                    reference_type='invoice',
                    reference_id=invoice.id,
                    created_by=current_user.id
                )
                db.session.add(movement)
                
                subtotal += item_total
            
            # Calculate totals
            discount_amount = subtotal * (invoice.discount_percentage / 100)
            tax_amount = (subtotal - discount_amount) * 0.15  # 15% VAT
            total_amount = subtotal - discount_amount + tax_amount
            
            invoice.subtotal = subtotal
            invoice.discount_amount = discount_amount
            invoice.tax_amount = tax_amount
            invoice.total_amount = total_amount
            invoice.remaining_amount = total_amount
            
            db.session.commit()
            flash('تم تحديث الفاتورة بنجاح', 'success')
            return redirect(url_for('invoices.view', id=invoice.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')
    
    customers = Customer.query.filter_by(is_active=True).all()
    return render_template('invoices/edit.html', invoice=invoice, customers=customers)

@invoices_bp.route('/cancel/<int:id>')
@login_required
def cancel(id):
    if current_user.role not in ['admin', 'manager']:
        flash('ليس لديك صلاحية لإلغاء الفواتير', 'error')
        return redirect(url_for('invoices.index'))
    
    invoice = Invoice.query.get_or_404(id)
    
    if invoice.status == 'cancelled':
        flash('الفاتورة ملغاة مسبقاً', 'warning')
        return redirect(url_for('invoices.view', id=id))
    
    try:
        # Restore stock for all items
        for item in invoice.items:
            product = Product.query.get(item.product_id)
            if product:
                product.current_stock += item.quantity
                
                # Create stock movement
                movement = StockMovement(
                    product_id=product.id,
                    movement_type='in',
                    quantity=item.quantity,
                    previous_stock=product.current_stock - item.quantity,
                    new_stock=product.current_stock,
                    reference_type='invoice_cancel',
                    reference_id=invoice.id,
                    notes=f'إلغاء فاتورة رقم {invoice.invoice_number}',
                    created_by=current_user.id
                )
                db.session.add(movement)
        
        # Update invoice status
        invoice.status = 'cancelled'
        
        # Update customer balance if applicable
        if invoice.customer_id:
            customer = Customer.query.get(invoice.customer_id)
            if customer:
                customer.current_balance -= invoice.remaining_amount
        
        db.session.commit()
        flash('تم إلغاء الفاتورة بنجاح', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ: {str(e)}', 'error')
    
    return redirect(url_for('invoices.view', id=id))

@invoices_bp.route('/print/<int:id>')
@login_required
def print_invoice(id):
    invoice = Invoice.query.get_or_404(id)
    return render_template('invoices/print.html', invoice=invoice)

@invoices_bp.route('/thermal_print/<int:id>')
@login_required
def thermal_print(id):
    invoice = Invoice.query.get_or_404(id)
    
    try:
        from escpos.printer import Usb
        from escpos.exceptions import USBNotFoundError
        
        # Try to connect to thermal printer
        p = Usb(0x04b8, 0x0202)  # Epson printer
        
        # Print header
        p.set(align='center', text_type='B', width=2, height=2)
        p.text("شركة المحاسبة\n")
        p.set(align='center', text_type='normal', width=1, height=1)
        p.text("Accounting Company\n")
        p.text("=" * 32 + "\n")
        
        # Invoice details
        p.set(align='left')
        p.text(f"فاتورة رقم: {invoice.invoice_number}\n")
        p.text(f"التاريخ: {invoice.created_at.strftime('%Y-%m-%d %H:%M')}\n")
        p.text(f"العميل: {invoice.customer_name or 'عميل نقدي'}\n")
        p.text("-" * 32 + "\n")
        
        # Items
        for item in invoice.items:
            p.text(f"{item.product_name}\n")
            p.text(f"{item.quantity} x {item.unit_price:.2f} = {item.total_price:.2f}\n")
        
        p.text("-" * 32 + "\n")
        
        # Totals
        p.text(f"المجموع الفرعي: {invoice.subtotal:.2f} ر.س\n")
        if invoice.discount_amount > 0:
            p.text(f"الخصم: {invoice.discount_amount:.2f} ر.س\n")
        p.text(f"الضريبة: {invoice.tax_amount:.2f} ر.س\n")
        p.set(text_type='B')
        p.text(f"الإجمالي: {invoice.total_amount:.2f} ر.س\n")
        
        p.text("\n")
        p.set(align='center', text_type='normal')
        p.text("شكراً لزيارتكم\n")
        p.text("Thank you for your visit\n")
        
        p.cut()
        p.close()
        
        flash('تم طباعة الفاتورة بنجاح', 'success')
        
    except USBNotFoundError:
        flash('لم يتم العثور على الطابعة الحرارية', 'error')
    except Exception as e:
        flash(f'خطأ في الطباعة: {str(e)}', 'error')
    
    return redirect(url_for('invoices.view', id=id))

def generate_invoice_number():
    """Generate unique invoice number"""
    today = datetime.now()
    prefix = f"INV{today.strftime('%Y%m%d')}"
    
    # Get last invoice number for today
    last_invoice = Invoice.query.filter(
        Invoice.invoice_number.like(f"{prefix}%")
    ).order_by(Invoice.id.desc()).first()
    
    if last_invoice:
        last_number = int(last_invoice.invoice_number[-4:])
        new_number = last_number + 1
    else:
        new_number = 1
    
    return f"{prefix}{new_number:04d}"
