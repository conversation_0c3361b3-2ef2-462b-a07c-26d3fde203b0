from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import Product, Customer, Invoice, InvoiceItem, StockMovement, CashTransaction, CashRegister, db
from datetime import datetime
import json

pos_bp = Blueprint('pos', __name__)

@pos_bp.route('/')
@login_required
def index():
    # Check if cash register is open
    current_register = CashRegister.query.filter_by(status='open').first()
    if not current_register:
        flash('يجب فتح الصندوق أولاً', 'warning')
        return redirect(url_for('pos.open_register'))
    
    return render_template('pos/index.html', register=current_register)

@pos_bp.route('/open_register', methods=['GET', 'POST'])
@login_required
def open_register():
    # Check if register is already open
    current_register = CashRegister.query.filter_by(status='open').first()
    if current_register:
        flash('الصندوق مفتوح بالفعل', 'info')
        return redirect(url_for('pos.index'))
    
    if request.method == 'POST':
        try:
            opening_balance = float(request.form.get('opening_balance', 0))
            
            register = CashRegister(
                opened_by=current_user.id,
                opening_balance=opening_balance,
                status='open'
            )
            
            db.session.add(register)
            db.session.commit()
            
            flash('تم فتح الصندوق بنجاح', 'success')
            return redirect(url_for('pos.index'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')
    
    return render_template('pos/open_register.html')

@pos_bp.route('/close_register', methods=['GET', 'POST'])
@login_required
def close_register():
    current_register = CashRegister.query.filter_by(status='open').first()
    if not current_register:
        flash('لا يوجد صندوق مفتوح', 'error')
        return redirect(url_for('pos.open_register'))
    
    if request.method == 'POST':
        try:
            closing_balance = float(request.form.get('closing_balance', 0))
            
            # Calculate expected balance
            total_sales = db.session.query(func.sum(CashTransaction.amount)).filter(
                CashTransaction.cash_register_id == current_register.id,
                CashTransaction.transaction_type == 'sale'
            ).scalar() or 0
            
            total_expenses = db.session.query(func.sum(CashTransaction.amount)).filter(
                CashTransaction.cash_register_id == current_register.id,
                CashTransaction.transaction_type == 'expense'
            ).scalar() or 0
            
            expected_balance = current_register.opening_balance + total_sales - total_expenses
            difference = closing_balance - expected_balance
            
            current_register.closing_balance = closing_balance
            current_register.expected_balance = expected_balance
            current_register.difference = difference
            current_register.closed_by = current_user.id
            current_register.closed_at = datetime.utcnow()
            current_register.status = 'closed'
            
            db.session.commit()
            
            flash('تم إغلاق الصندوق بنجاح', 'success')
            return redirect(url_for('pos.register_report', id=current_register.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')
    
    # Calculate current totals
    total_sales = db.session.query(func.sum(CashTransaction.amount)).filter(
        CashTransaction.cash_register_id == current_register.id,
        CashTransaction.transaction_type == 'sale'
    ).scalar() or 0
    
    total_expenses = db.session.query(func.sum(CashTransaction.amount)).filter(
        CashTransaction.cash_register_id == current_register.id,
        CashTransaction.transaction_type == 'expense'
    ).scalar() or 0
    
    expected_balance = current_register.opening_balance + total_sales - total_expenses
    
    return render_template('pos/close_register.html',
                         register=current_register,
                         total_sales=total_sales,
                         total_expenses=total_expenses,
                         expected_balance=expected_balance)

@pos_bp.route('/create_invoice', methods=['POST'])
@login_required
def create_invoice():
    try:
        # Parse request data
        data = request.get_json()
        items = data.get('items', [])
        customer_name = data.get('customer_name', 'عميل نقدي')
        customer_id = data.get('customer_id')
        discount_percentage = float(data.get('discount_percentage', 0))
        payment_method = data.get('payment_method', 'cash')
        notes = data.get('notes', '')
        
        if not items:
            return jsonify({'success': False, 'message': 'يجب إضافة منتج واحد على الأقل'})
        
        # Check cash register
        current_register = CashRegister.query.filter_by(status='open').first()
        if not current_register:
            return jsonify({'success': False, 'message': 'يجب فتح الصندوق أولاً'})
        
        # Generate invoice number
        invoice_number = generate_invoice_number()
        
        # Create invoice
        invoice = Invoice(
            invoice_number=invoice_number,
            customer_id=customer_id,
            customer_name=customer_name,
            discount_percentage=discount_percentage,
            payment_method=payment_method,
            notes=notes,
            created_by=current_user.id
        )
        
        db.session.add(invoice)
        db.session.flush()  # Get invoice ID
        
        subtotal = 0
        
        # Process items
        for item_data in items:
            product = Product.query.get(item_data['product_id'])
            if not product:
                continue
            
            quantity = float(item_data['quantity'])
            unit_price = float(item_data['unit_price'])
            item_total = quantity * unit_price
            
            # Check stock
            if product.current_stock < quantity:
                return jsonify({
                    'success': False, 
                    'message': f'المخزون غير كافي للمنتج: {product.name_ar}'
                })
            
            # Create invoice item
            invoice_item = InvoiceItem(
                invoice_id=invoice.id,
                product_id=product.id,
                product_name=product.name_ar,
                product_barcode=product.barcode,
                unit_price=unit_price,
                quantity=quantity,
                total_price=item_total
            )
            db.session.add(invoice_item)
            
            # Update stock
            product.current_stock -= quantity
            
            # Create stock movement
            movement = StockMovement(
                product_id=product.id,
                movement_type='out',
                quantity=-quantity,
                previous_stock=product.current_stock + quantity,
                new_stock=product.current_stock,
                reference_type='invoice',
                reference_id=invoice.id,
                created_by=current_user.id
            )
            db.session.add(movement)
            
            subtotal += item_total
        
        # Calculate totals
        discount_amount = subtotal * (discount_percentage / 100)
        tax_amount = (subtotal - discount_amount) * 0.15  # 15% VAT
        total_amount = subtotal - discount_amount + tax_amount
        
        invoice.subtotal = subtotal
        invoice.discount_amount = discount_amount
        invoice.tax_amount = tax_amount
        invoice.total_amount = total_amount
        
        # Handle payment
        if payment_method == 'cash':
            invoice.paid_amount = total_amount
            invoice.remaining_amount = 0
            invoice.payment_status = 'paid'
            
            # Add cash transaction
            transaction = CashTransaction(
                cash_register_id=current_register.id,
                transaction_type='sale',
                amount=total_amount,
                description=f'فاتورة رقم {invoice_number}',
                reference_type='invoice',
                reference_id=invoice.id,
                created_by=current_user.id
            )
            db.session.add(transaction)
            
        elif payment_method == 'credit':
            invoice.paid_amount = 0
            invoice.remaining_amount = total_amount
            invoice.payment_status = 'pending'
            
            # Update customer balance
            if customer_id:
                customer = Customer.query.get(customer_id)
                if customer:
                    customer.current_balance += total_amount
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'invoice_id': invoice.id,
            'invoice_number': invoice_number,
            'total_amount': total_amount
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@pos_bp.route('/customer_display')
@login_required
def customer_display():
    """Customer facing display screen"""
    return render_template('pos/customer_display.html')

@pos_bp.route('/register_report/<int:id>')
@login_required
def register_report(id):
    register = CashRegister.query.get_or_404(id)
    
    # Get transactions
    transactions = CashTransaction.query.filter_by(
        cash_register_id=id
    ).order_by(CashTransaction.created_at.desc()).all()
    
    # Calculate totals
    total_sales = sum(t.amount for t in transactions if t.transaction_type == 'sale')
    total_expenses = sum(t.amount for t in transactions if t.transaction_type == 'expense')
    
    return render_template('pos/register_report.html',
                         register=register,
                         transactions=transactions,
                         total_sales=total_sales,
                         total_expenses=total_expenses)

def generate_invoice_number():
    """Generate unique invoice number"""
    today = datetime.now()
    prefix = f"INV{today.strftime('%Y%m%d')}"
    
    # Get last invoice number for today
    last_invoice = Invoice.query.filter(
        Invoice.invoice_number.like(f"{prefix}%")
    ).order_by(Invoice.id.desc()).first()
    
    if last_invoice:
        last_number = int(last_invoice.invoice_number[-4:])
        new_number = last_number + 1
    else:
        new_number = 1
    
    return f"{prefix}{new_number:04d}"
