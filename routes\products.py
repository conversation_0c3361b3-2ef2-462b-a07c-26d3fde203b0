from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from models import Product, Category, Unit, StockStatus, StockMovement, db
import os
import uuid
import qrcode
from barcode import Code128
from barcode.writer import ImageWriter
import io
import base64
from PIL import Image

products_bp = Blueprint('products', __name__)

@products_bp.route('/')
@login_required
def index():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    category_id = request.args.get('category', type=int)
    
    query = Product.query.filter_by(is_active=True)
    
    if search:
        query = query.filter(
            (Product.name_ar.contains(search)) |
            (Product.name_en.contains(search)) |
            (Product.barcode.contains(search))
        )
    
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    products = query.order_by(Product.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    categories = Category.query.all()
    
    return render_template('products/index.html', 
                         products=products, 
                         categories=categories,
                         search=search,
                         selected_category=category_id)

@products_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    if request.method == 'POST':
        try:
            # Generate barcode if not provided
            barcode = request.form.get('barcode')
            if not barcode:
                barcode = generate_barcode()
            
            # Check if barcode already exists
            existing = Product.query.filter_by(barcode=barcode).first()
            if existing:
                flash('الباركود موجود مسبقاً', 'error')
                return redirect(url_for('products.add'))
            
            product = Product(
                name_ar=request.form.get('name_ar'),
                name_en=request.form.get('name_en'),
                barcode=barcode,
                sku=request.form.get('sku'),
                description=request.form.get('description'),
                cost_price=float(request.form.get('cost_price', 0)),
                selling_price=float(request.form.get('selling_price', 0)),
                min_selling_price=float(request.form.get('min_selling_price', 0)),
                current_stock=float(request.form.get('current_stock', 0)),
                min_stock_level=float(request.form.get('min_stock_level', 0)),
                max_stock_level=float(request.form.get('max_stock_level', 1000)),
                category_id=int(request.form.get('category_id')),
                unit_id=int(request.form.get('unit_id')),
                stock_status_id=int(request.form.get('stock_status_id'))
            )
            
            # Handle image upload
            if 'image' in request.files:
                file = request.files['image']
                if file and file.filename:
                    filename = secure_filename(file.filename)
                    filename = f"{uuid.uuid4()}_{filename}"
                    file_path = os.path.join('static/uploads', filename)
                    file.save(file_path)
                    product.image_path = filename
            
            db.session.add(product)
            db.session.flush()  # Get the product ID
            
            # Add initial stock movement if stock > 0
            if product.current_stock > 0:
                movement = StockMovement(
                    product_id=product.id,
                    movement_type='initial',
                    quantity=product.current_stock,
                    previous_stock=0,
                    new_stock=product.current_stock,
                    notes='مخزون أولي',
                    created_by=current_user.id
                )
                db.session.add(movement)
            
            db.session.commit()
            flash('تم إضافة المنتج بنجاح', 'success')
            return redirect(url_for('products.index'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')
    
    categories = Category.query.all()
    units = Unit.query.all()
    stock_statuses = StockStatus.query.all()
    
    return render_template('products/add.html',
                         categories=categories,
                         units=units,
                         stock_statuses=stock_statuses)

@products_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit(id):
    product = Product.query.get_or_404(id)
    
    if request.method == 'POST':
        try:
            # Check barcode uniqueness
            barcode = request.form.get('barcode')
            existing = Product.query.filter(
                Product.barcode == barcode,
                Product.id != id
            ).first()
            if existing:
                flash('الباركود موجود مسبقاً', 'error')
                return redirect(url_for('products.edit', id=id))
            
            product.name_ar = request.form.get('name_ar')
            product.name_en = request.form.get('name_en')
            product.barcode = barcode
            product.sku = request.form.get('sku')
            product.description = request.form.get('description')
            product.cost_price = float(request.form.get('cost_price', 0))
            product.selling_price = float(request.form.get('selling_price', 0))
            product.min_selling_price = float(request.form.get('min_selling_price', 0))
            product.min_stock_level = float(request.form.get('min_stock_level', 0))
            product.max_stock_level = float(request.form.get('max_stock_level', 1000))
            product.category_id = int(request.form.get('category_id'))
            product.unit_id = int(request.form.get('unit_id'))
            product.stock_status_id = int(request.form.get('stock_status_id'))
            
            # Handle image upload
            if 'image' in request.files:
                file = request.files['image']
                if file and file.filename:
                    # Delete old image
                    if product.image_path:
                        old_path = os.path.join('static/uploads', product.image_path)
                        if os.path.exists(old_path):
                            os.remove(old_path)
                    
                    filename = secure_filename(file.filename)
                    filename = f"{uuid.uuid4()}_{filename}"
                    file_path = os.path.join('static/uploads', filename)
                    file.save(file_path)
                    product.image_path = filename
            
            db.session.commit()
            flash('تم تحديث المنتج بنجاح', 'success')
            return redirect(url_for('products.index'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')
    
    categories = Category.query.all()
    units = Unit.query.all()
    stock_statuses = StockStatus.query.all()
    
    return render_template('products/edit.html',
                         product=product,
                         categories=categories,
                         units=units,
                         stock_statuses=stock_statuses)

@products_bp.route('/delete/<int:id>')
@login_required
def delete(id):
    if current_user.role not in ['admin', 'manager']:
        flash('ليس لديك صلاحية لحذف المنتجات', 'error')
        return redirect(url_for('products.index'))
    
    product = Product.query.get_or_404(id)
    product.is_active = False
    db.session.commit()
    
    flash('تم حذف المنتج بنجاح', 'success')
    return redirect(url_for('products.index'))

@products_bp.route('/generate_barcode')
@login_required
def generate_barcode_route():
    barcode = generate_barcode()
    return jsonify({'barcode': barcode})

@products_bp.route('/barcode_image/<barcode>')
@login_required
def barcode_image(barcode):
    # Generate barcode image
    code = Code128(barcode, writer=ImageWriter())
    buffer = io.BytesIO()
    code.write(buffer)
    buffer.seek(0)
    
    # Convert to base64
    img_data = base64.b64encode(buffer.getvalue()).decode()
    return jsonify({'image': f'data:image/png;base64,{img_data}'})

@products_bp.route('/search')
@login_required
def search():
    query = request.args.get('q', '')
    if len(query) < 2:
        return jsonify([])
    
    products = Product.query.filter(
        Product.is_active == True,
        (Product.name_ar.contains(query)) |
        (Product.name_en.contains(query)) |
        (Product.barcode.contains(query))
    ).limit(10).all()
    
    return jsonify([{
        'id': p.id,
        'name': p.name_ar,
        'barcode': p.barcode,
        'price': p.selling_price,
        'stock': p.current_stock,
        'unit': p.unit.symbol
    } for p in products])

def generate_barcode():
    """Generate a unique barcode"""
    import random
    while True:
        barcode = ''.join([str(random.randint(0, 9)) for _ in range(13)])
        if not Product.query.filter_by(barcode=barcode).first():
            return barcode
