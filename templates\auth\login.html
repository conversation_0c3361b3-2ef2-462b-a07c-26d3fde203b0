<!DOCTYPE html>
<html lang="{{ 'ar' if current_language == 'ar' else 'en' }}" dir="{{ 'rtl' if current_language == 'ar' else 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - {{ COMPANY_NAME }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Arabic Font -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 1rem 3rem rgba(0,0,0,0.175);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .login-body {
            padding: 2rem;
        }
        .form-control {
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
            border: 2px solid #e9ecef;
            transition: all 0.3s;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 0.75rem;
            padding: 0.75rem;
            font-weight: 600;
            transition: all 0.3s;
        }
        .btn-login:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
        }
        .language-switcher {
            position: absolute;
            top: 1rem;
            right: 1rem;
        }
        .language-switcher a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            background: rgba(255,255,255,0.1);
            transition: all 0.3s;
        }
        .language-switcher a:hover {
            background: rgba(255,255,255,0.2);
        }
    </style>
</head>
<body>
    <!-- Language switcher -->
    <div class="language-switcher">
        <a href="{{ url_for('set_language', language='ar') }}" class="{{ 'fw-bold' if current_language == 'ar' }}">عربي</a>
        <a href="{{ url_for('set_language', language='en') }}" class="{{ 'fw-bold' if current_language == 'en' }}">EN</a>
    </div>
    
    <div class="login-card">
        <div class="login-header">
            <i class="fas fa-calculator fa-3x mb-3"></i>
            <h3 class="mb-1">{{ COMPANY_NAME }}</h3>
            <p class="mb-0 opacity-75">{{ COMPANY_NAME_EN }}</p>
        </div>
        
        <div class="login-body">
            <!-- Flash messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <form method="POST">
                <div class="mb-3">
                    <label for="username" class="form-label">
                        <i class="fas fa-user me-2"></i>
                        {{ 'اسم المستخدم' if current_language == 'ar' else 'Username' }}
                    </label>
                    <input type="text" class="form-control" id="username" name="username" required 
                           placeholder="{{ 'أدخل اسم المستخدم' if current_language == 'ar' else 'Enter username' }}">
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock me-2"></i>
                        {{ 'كلمة المرور' if current_language == 'ar' else 'Password' }}
                    </label>
                    <input type="password" class="form-control" id="password" name="password" required 
                           placeholder="{{ 'أدخل كلمة المرور' if current_language == 'ar' else 'Enter password' }}">
                </div>
                
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="remember" name="remember">
                    <label class="form-check-label" for="remember">
                        {{ 'تذكرني' if current_language == 'ar' else 'Remember me' }}
                    </label>
                </div>
                
                <button type="submit" class="btn btn-primary btn-login w-100">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    {{ 'تسجيل الدخول' if current_language == 'ar' else 'Login' }}
                </button>
            </form>
            
            <div class="text-center mt-4">
                <small class="text-muted">
                    {{ 'المستخدم الافتراضي: admin | كلمة المرور: admin123' if current_language == 'ar' else 'Default user: admin | Password: admin123' }}
                </small>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
