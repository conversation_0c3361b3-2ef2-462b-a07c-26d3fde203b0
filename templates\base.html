<!DOCTYPE html>
<html lang="{{ 'ar' if current_language == 'ar' else 'en' }}" dir="{{ 'rtl' if current_language == 'ar' else 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ COMPANY_NAME }}{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    {% if current_language == 'ar' %}
    <!-- Arabic Font -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .navbar-brand { font-weight: 700; }
        .btn { font-weight: 600; }
    </style>
    {% endif %}
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
            transform: translateX({{ '5px' if current_language == 'ar' else '-5px' }});
        }
        .main-content {
            background: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
            transition: all 0.3s;
        }
        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .table th {
            background: #f8f9fa;
            border-top: none;
            font-weight: 600;
        }
        .navbar {
            background: white !important;
            box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
        }
        .alert {
            border: none;
            border-radius: 0.75rem;
        }
        .form-control:focus,
        .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">{{ COMPANY_NAME }}</h4>
                        <small class="text-white-50">{{ COMPANY_NAME_EN }}</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.endpoint == 'dashboard.index' }}" 
                               href="{{ url_for('dashboard.index') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                {{ 'لوحة التحكم' if current_language == 'ar' else 'Dashboard' }}
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'pos' in request.endpoint }}" 
                               href="{{ url_for('pos.index') }}">
                                <i class="fas fa-cash-register me-2"></i>
                                {{ 'نقطة البيع' if current_language == 'ar' else 'POS' }}
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'products' in request.endpoint }}" 
                               href="{{ url_for('products.index') }}">
                                <i class="fas fa-box me-2"></i>
                                {{ 'المنتجات' if current_language == 'ar' else 'Products' }}
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'invoices' in request.endpoint }}" 
                               href="{{ url_for('invoices.index') }}">
                                <i class="fas fa-file-invoice me-2"></i>
                                {{ 'الفواتير' if current_language == 'ar' else 'Invoices' }}
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'customers' in request.endpoint }}" 
                               href="{{ url_for('customers.index') }}">
                                <i class="fas fa-users me-2"></i>
                                {{ 'العملاء' if current_language == 'ar' else 'Customers' }}
                            </a>
                        </li>
                        
                        {% if current_user.role in ['admin', 'manager'] %}
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-bs-toggle="collapse" data-bs-target="#reportsMenu">
                                <i class="fas fa-chart-bar me-2"></i>
                                {{ 'التقارير' if current_language == 'ar' else 'Reports' }}
                                <i class="fas fa-chevron-down ms-auto"></i>
                            </a>
                            <div class="collapse" id="reportsMenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-chart-line me-2"></i>
                                            {{ 'تقرير المبيعات' if current_language == 'ar' else 'Sales Report' }}
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-warehouse me-2"></i>
                                            {{ 'تقرير المخزون' if current_language == 'ar' else 'Inventory Report' }}
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        {% endif %}
                    </ul>
                    
                    <hr class="text-white-50">
                    
                    <div class="dropdown">
                        <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle" 
                           data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-2"></i>
                            <strong>{{ current_user.username }}</strong>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-dark text-small shadow">
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                                <i class="fas fa-user me-2"></i>{{ 'الملف الشخصي' if current_language == 'ar' else 'Profile' }}
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>{{ 'تسجيل الخروج' if current_language == 'ar' else 'Logout' }}
                            </a></li>
                        </ul>
                    </div>
                </div>
            </nav>
            
            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Top navbar -->
                <nav class="navbar navbar-expand-lg navbar-light bg-white mb-4">
                    <div class="container-fluid">
                        <button class="navbar-toggler d-md-none" type="button" data-bs-toggle="collapse" 
                                data-bs-target="#sidebarMenu">
                            <span class="navbar-toggler-icon"></span>
                        </button>
                        
                        <div class="navbar-nav ms-auto">
                            <!-- Language switcher -->
                            <div class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                                    <i class="fas fa-globe me-1"></i>
                                    {{ 'العربية' if current_language == 'ar' else 'English' }}
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{{ url_for('set_language', language='ar') }}">العربية</a></li>
                                    <li><a class="dropdown-item" href="{{ url_for('set_language', language='en') }}">English</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>
                
                <!-- Flash messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <!-- Page content -->
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
