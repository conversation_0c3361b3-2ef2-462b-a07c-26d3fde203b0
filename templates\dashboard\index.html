{% extends "base.html" %}

{% block title %}لوحة التحكم - {{ COMPANY_NAME }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>
        {{ 'لوحة التحكم' if current_language == 'ar' else 'Dashboard' }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-calendar me-1"></i>
                {{ 'اليوم' if current_language == 'ar' else 'Today' }}
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {{ 'مبيعات اليوم' if current_language == 'ar' else "Today's Sales" }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ today_sales|currency }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {{ 'مبيعات الشهر' if current_language == 'ar' else 'Monthly Sales' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ monthly_sales|currency }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {{ 'إجمالي المنتجات' if current_language == 'ar' else 'Total Products' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_products }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-box fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {{ 'منتجات قليلة المخزون' if current_language == 'ar' else 'Low Stock Products' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ low_stock_products }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    {{ 'مبيعات آخر 7 أيام' if current_language == 'ar' else 'Sales Last 7 Days' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="salesChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    {{ 'أفضل المنتجات مبيعاً' if current_language == 'ar' else 'Top Selling Products' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="topProductsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Row -->
<div class="row">
    <!-- Recent Invoices -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    {{ 'آخر الفواتير' if current_language == 'ar' else 'Recent Invoices' }}
                </h6>
            </div>
            <div class="card-body">
                {% if recent_invoices %}
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>{{ 'رقم الفاتورة' if current_language == 'ar' else 'Invoice #' }}</th>
                                <th>{{ 'العميل' if current_language == 'ar' else 'Customer' }}</th>
                                <th>{{ 'المبلغ' if current_language == 'ar' else 'Amount' }}</th>
                                <th>{{ 'التاريخ' if current_language == 'ar' else 'Date' }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in recent_invoices %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('invoices.view', id=invoice.id) }}" class="text-decoration-none">
                                        {{ invoice.invoice_number }}
                                    </a>
                                </td>
                                <td>{{ invoice.customer_name or 'عميل نقدي' }}</td>
                                <td>{{ invoice.total_amount|currency }}</td>
                                <td>{{ invoice.created_at|datetime }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                    <p class="text-muted">{{ 'لا توجد فواتير حديثة' if current_language == 'ar' else 'No recent invoices' }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Low Stock Products -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">
                    {{ 'منتجات قليلة المخزون' if current_language == 'ar' else 'Low Stock Products' }}
                </h6>
            </div>
            <div class="card-body">
                {% if low_stock_list %}
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>{{ 'المنتج' if current_language == 'ar' else 'Product' }}</th>
                                <th>{{ 'المخزون الحالي' if current_language == 'ar' else 'Current Stock' }}</th>
                                <th>{{ 'الحد الأدنى' if current_language == 'ar' else 'Min Level' }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in low_stock_list %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('products.edit', id=product.id) }}" class="text-decoration-none">
                                        {{ product.name_ar }}
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'danger' if product.current_stock == 0 else 'warning' }}">
                                        {{ product.current_stock }} {{ product.unit.symbol }}
                                    </span>
                                </td>
                                <td>{{ product.min_stock_level }} {{ product.unit.symbol }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <p class="text-muted">{{ 'جميع المنتجات متوفرة' if current_language == 'ar' else 'All products are well stocked' }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Cash Register Status -->
{% if current_register %}
<div class="row">
    <div class="col-12">
        <div class="alert alert-info" role="alert">
            <i class="fas fa-cash-register me-2"></i>
            {{ 'الصندوق مفتوح' if current_language == 'ar' else 'Cash register is open' }} - 
            {{ 'الرصيد الافتتاحي:' if current_language == 'ar' else 'Opening balance:' }} {{ current_register.opening_balance|currency }}
            <a href="{{ url_for('pos.close_register') }}" class="btn btn-sm btn-outline-primary ms-2">
                {{ 'إغلاق الصندوق' if current_language == 'ar' else 'Close Register' }}
            </a>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Sales Chart
fetch('{{ url_for("dashboard.sales_chart") }}')
    .then(response => response.json())
    .then(data => {
        const ctx = document.getElementById('salesChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.map(item => item.date),
                datasets: [{
                    label: '{{ "المبيعات" if current_language == "ar" else "Sales" }}',
                    data: data.map(item => item.sales),
                    borderColor: 'rgb(102, 126, 234)',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    });

// Top Products Chart
fetch('{{ url_for("dashboard.top_products") }}')
    .then(response => response.json())
    .then(data => {
        const ctx = document.getElementById('topProductsChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.map(item => item.name),
                datasets: [{
                    data: data.map(item => item.sales),
                    backgroundColor: [
                        '#667eea',
                        '#764ba2',
                        '#f093fb',
                        '#f5576c',
                        '#4facfe'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    });
</script>
{% endblock %}
