from PyQt6.QtWidgets import QMain<PERSON>indow, QW<PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QListWidget, QStackedWidget
from PyQt6.QtCore import Qt
from config import DARK_STYLE, APP_NAME, LANGUAGE

class Sidebar(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedWidth(200)
        layout = QVBoxLayout()
        self.menu = QListWidget()
        self.menu.addItems([
            'المنتجات' if LANGUAGE == 'ar' else 'Products',
            'فواتير الإدخال' if LANGUAGE == 'ar' else 'Purchase Invoices',
            'فواتير الإخراج' if LANGUAGE == 'ar' else 'Sales Invoices',
            'التقارير' if LANGUAGE == 'ar' else 'Reports',
            'الإعدادات' if LANGUAGE == 'ar' else 'Settings',
        ])
        layout.addWidget(self.menu)
        layout.addStretch()
        self.setLayout(layout)

class MainAppWindow(QMainWindow):
    def __init__(self, role):
        super().__init__()
        self.setWindowTitle(APP_NAME[LANGUAGE])
        self.setStyleSheet(DARK_STYLE)
        self.setGeometry(100, 100, 1200, 700)
        main_widget = QWidget()
        main_layout = QHBoxLayout()
        self.sidebar = Sidebar()
        self.stack = QStackedWidget()
        # Placeholder pages
        from ui.products import ProductManager
        from ui.purchase_invoices import PurchaseInvoiceManager
        from ui.sales_invoices import SalesInvoiceManager
        from ui.reports import ReportsManager
        self.pages = {
            'products': ProductManager(),
            'purchase': PurchaseInvoiceManager(),
            'sales': SalesInvoiceManager(),
            'reports': ReportsManager(),
            'settings': QLabel('صفحة الإعدادات' if LANGUAGE == 'ar' else 'Settings Page'),
        }
        for i, page in enumerate(self.pages.values()):
            if isinstance(page, QLabel):
                page.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.stack.addWidget(page)
        main_layout.addWidget(self.sidebar)
        main_layout.addWidget(self.stack)
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)
        self.sidebar.menu.currentRowChanged.connect(self.stack.setCurrentIndex)
