# نموذج إدارة المنتجات (واجهة فقط - سيتم ربطها لاحقاً بالبيانات)
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QTextEdit, QComboBox, QFileDialog, QTableWidget, QTableWidgetItem
from PyQt6.QtGui import QPixmap
from PyQt6.QtCore import Qt
from config import LANGUAGE

class ProductManager(QWidget):
    def __init__(self):
        super().__init__()
        layout = QVBoxLayout()
        form_layout = QHBoxLayout()
        # يسار: صورة المنتج
        self.img_label = QLabel()
        self.img_label.setFixedSize(120, 120)
        self.img_label.setStyleSheet('border:1px solid #44475a;')
        self.img_btn = QPushButton('تحميل صورة' if LANGUAGE == 'ar' else 'Upload Image')
        self.img_btn.clicked.connect(self.load_image)
        img_layout = QVBoxLayout()
        img_layout.addWidget(self.img_label)
        img_layout.addWidget(self.img_btn)
        # يمين: حقول الإدخال
        fields_layout = QVBoxLayout()
        self.code_input = QLineEdit()
        self.code_input.setPlaceholderText('كود المادة')
        self.barcode_btn = QPushButton('توليد باركود')
        self.barcode_btn.clicked.connect(self.generate_barcode)
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText('اسم المادة')
        self.unit_combo = QComboBox()
        self.unit_combo.setEditable(True)
        self.unit_btn = QPushButton('إضافة وحدة')
        self.unit_btn.clicked.connect(self.add_unit)
        self.qty_input = QLineEdit()
        self.qty_input.setPlaceholderText('الكمية المتوفرة')
        self.status_combo = QComboBox()
        self.status_combo.setEditable(True)
        self.status_btn = QPushButton('إضافة حالة')
        self.status_btn.clicked.connect(self.add_status)
        self.cat_combo = QComboBox()
        self.cat_combo.setEditable(True)
        self.cat_btn = QPushButton('إضافة تصنيف')
        self.cat_btn.clicked.connect(self.add_category)
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText('ملاحظات')
        # أزرار العمليات
        btns_layout = QHBoxLayout()
        self.add_btn = QPushButton('إضافة')
        self.add_btn.clicked.connect(self.add_product)
        self.edit_btn = QPushButton('تعديل')
        self.edit_btn.clicked.connect(self.edit_product)
        self.delete_btn = QPushButton('حذف')
        self.delete_btn.clicked.connect(self.delete_product)
        self.clear_btn = QPushButton('تفريغ')
        self.clear_btn.clicked.connect(self.clear_fields)
        btns_layout.addWidget(self.add_btn)
        btns_layout.addWidget(self.edit_btn)
        btns_layout.addWidget(self.delete_btn)
        btns_layout.addWidget(self.clear_btn)
        # تجميع الحقول
        fields_layout.addWidget(self.code_input)
        fields_layout.addWidget(self.barcode_btn)
        fields_layout.addWidget(self.name_input)
        fields_layout.addWidget(self.unit_combo)
        fields_layout.addWidget(self.unit_btn)
        fields_layout.addWidget(self.qty_input)
        fields_layout.addWidget(self.status_combo)
        fields_layout.addWidget(self.status_btn)
        fields_layout.addWidget(self.cat_combo)
        fields_layout.addWidget(self.cat_btn)
        fields_layout.addWidget(self.notes_input)
        fields_layout.addLayout(btns_layout)
        form_layout.addLayout(fields_layout)
        form_layout.addLayout(img_layout)
        layout.addLayout(form_layout)
        # جدول المنتجات
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText('بحث عن المنتجات')
        self.search_btn = QPushButton('بحث')
        self.search_btn.clicked.connect(self.search_products)
        search_layout = QHBoxLayout()
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(self.search_btn)
        layout.addLayout(search_layout)
        self.table = QTableWidget(0, 7)
        self.table.setHorizontalHeaderLabels([
            'كود المادة', 'اسم المادة', 'الوحدة', 'الكمية', 'الحالة', 'التصنيف', 'ملاحظات'
        ] if LANGUAGE == 'ar' else [
            'Code', 'Name', 'Unit', 'Quantity', 'Status', 'Category', 'Notes'
        ])
        layout.addWidget(self.table)
        self.setLayout(layout)

    def load_image(self):
        file, _ = QFileDialog.getOpenFileName(self, 'اختر صورة' if LANGUAGE == 'ar' else 'Select Image', '', 'Images (*.png *.jpg *.jpeg)')
        if file:
            pixmap = QPixmap(file)
            self.img_label.setPixmap(pixmap.scaled(120, 120, Qt.AspectRatioMode.KeepAspectRatio))

    def generate_barcode(self):
        import random, string
        code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
        self.code_input.setText(code)

    def add_unit(self):
        from models.products_model import add_unit, get_units
        name = self.unit_combo.currentText().strip()
        if name:
            add_unit(name)
            self.unit_combo.clear()
            self.unit_combo.addItems(get_units())

    def add_status(self):
        from models.products_model import add_status, get_statuses
        name = self.status_combo.currentText().strip()
        if name:
            add_status(name)
            self.status_combo.clear()
            self.status_combo.addItems(get_statuses())

    def add_category(self):
        from models.products_model import add_category, get_categories
        name = self.cat_combo.currentText().strip()
        if name:
            add_category(name)
            self.cat_combo.clear()
            self.cat_combo.addItems(get_categories())

    def add_product(self):
        from models.products_model import add_product
        code = self.code_input.text().strip()
        name = self.name_input.text().strip()
        unit = self.unit_combo.currentText().strip()
        qty = self.qty_input.text().strip()
        status = self.status_combo.currentText().strip()
        cat = self.cat_combo.currentText().strip()
        notes = self.notes_input.toPlainText().strip()
        img = self.img_label.pixmap().toImage() if self.img_label.pixmap() else None
        img_path = ''
        if img:
            img_path = f'assets/{code}.png'
            img.save(img_path)
        add_product(code, name, unit, int(qty) if qty.isdigit() else 0, status, cat, notes, img_path)
        self.load_products()
        self.clear_fields()

    def edit_product(self):
        from models.products_model import update_product
        code = self.code_input.text().strip()
        name = self.name_input.text().strip()
        unit = self.unit_combo.currentText().strip()
        qty = self.qty_input.text().strip()
        status = self.status_combo.currentText().strip()
        cat = self.cat_combo.currentText().strip()
        notes = self.notes_input.toPlainText().strip()
        img = self.img_label.pixmap().toImage() if self.img_label.pixmap() else None
        img_path = ''
        if img:
            img_path = f'assets/{code}.png'
            img.save(img_path)
        update_product(code, name, unit, int(qty) if qty.isdigit() else 0, status, cat, notes, img_path)
        self.load_products()
        self.clear_fields()

    def delete_product(self):
        from models.products_model import delete_product
        code = self.code_input.text().strip()
        if code:
            delete_product(code)
            self.load_products()
            self.clear_fields()

    def clear_fields(self):
        self.code_input.clear()
        self.name_input.clear()
        self.qty_input.clear()
        self.notes_input.clear()
        self.img_label.clear()
        self.unit_combo.setCurrentIndex(-1)
        self.status_combo.setCurrentIndex(-1)
        self.cat_combo.setCurrentIndex(-1)

    def search_products(self):
        self.load_products(self.search_input.text().strip())

    def load_products(self, search=None):
        from models.products_model import get_products, get_units, get_statuses, get_categories
        self.table.setRowCount(0)
        products = get_products(search)
        for row_data in products:
            row = self.table.rowCount()
            self.table.insertRow(row)
            for col, value in enumerate(row_data):
                self.table.setItem(row, col, QTableWidgetItem(str(value)))
        # تحميل القوائم المنسدلة
        self.unit_combo.clear()
        self.unit_combo.addItems(get_units())
        self.status_combo.clear()
        self.status_combo.addItems(get_statuses())
        self.cat_combo.clear()
        self.cat_combo.addItems(get_categories())

    def showEvent(self, event):
        self.load_products()
        super().showEvent(event)
        file, _ = QFileDialog.getOpenFileName(self, 'اختر صورة' if LANGUAGE == 'ar' else 'Select Image', '', 'Images (*.png *.jpg *.jpeg)')
        if file:
            pixmap = QPixmap(file)
            self.img_label.setPixmap(pixmap.scaled(120, 120, Qt.AspectRatioMode.KeepAspectRatio))
