from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QComboBox, QTableWidget, QTableWidgetItem, QDateEdit, QMessageBox
from PyQt6.QtCore import Qt, QDate
from config import LANGUAGE
import sqlite3

class PurchaseInvoiceManager(QWidget):
    def __init__(self):
        super().__init__()
        layout = QVBoxLayout()
        # رأس الفاتورة
        head_layout = QHBoxLayout()
        self.inv_num_input = QLineEdit()
        self.inv_num_input.setPlaceholderText('رقم الفاتورة' if LANGUAGE == 'ar' else 'Invoice Number')
        self.date_input = QDateEdit()
        self.date_input.setDate(QDate.currentDate())
        self.supplier_combo = QComboBox()
        self.supplier_combo.setEditable(True)
        self.supplier_btn = QPushButton('إضافة مورد' if LANGUAGE == 'ar' else 'Add Supplier')
        self.supplier_btn.clicked.connect(self.add_supplier)
        head_layout.addWidget(self.inv_num_input)
        head_layout.addWidget(self.date_input)
        head_layout.addWidget(self.supplier_combo)
        head_layout.addWidget(self.supplier_btn)
        layout.addLayout(head_layout)
        # جدول المواد
        self.table = QTableWidget(0, 5)
        self.table.setHorizontalHeaderLabels([
            'كود المادة', 'اسم المادة', 'الكمية المدخلة', 'السعر', 'الإجمالي'
        ] if LANGUAGE == 'ar' else [
            'Code', 'Name', 'Quantity', 'Price', 'Total'
        ])
        layout.addWidget(self.table)
        # أزرار العمليات
        btns_layout = QHBoxLayout()
        self.add_row_btn = QPushButton('إضافة مادة' if LANGUAGE == 'ar' else 'Add Item')
        self.save_btn = QPushButton('حفظ الفاتورة' if LANGUAGE == 'ar' else 'Save Invoice')
        self.clear_btn = QPushButton('تفريغ' if LANGUAGE == 'ar' else 'Clear')
        self.add_row_btn.clicked.connect(self.add_row)
        self.save_btn.clicked.connect(self.save_invoice)
        self.clear_btn.clicked.connect(self.clear_all)
        btns_layout.addWidget(self.add_row_btn)
        btns_layout.addWidget(self.save_btn)
        btns_layout.addWidget(self.clear_btn)
        layout.addLayout(btns_layout)
        self.setLayout(layout)
        self.load_suppliers()

    def add_row(self):
        row = self.table.rowCount()
        self.table.insertRow(row)
        for col in range(self.table.columnCount()):
            self.table.setItem(row, col, QTableWidgetItem(''))

    def add_supplier(self):
        name = self.supplier_combo.currentText().strip()
        if name:
            conn = sqlite3.connect('db/warehouse.db')
            c = conn.cursor()
            try:
                c.execute('INSERT INTO suppliers (name) VALUES (?)', (name,))
                conn.commit()
            except sqlite3.IntegrityError:
                pass
            conn.close()
            self.load_suppliers()

    def load_suppliers(self):
        self.supplier_combo.clear()
        conn = sqlite3.connect('db/warehouse.db')
        c = conn.cursor()
        c.execute('SELECT name FROM suppliers')
        self.supplier_combo.addItems([row[0] for row in c.fetchall()])
        conn.close()

    def save_invoice(self):
        inv_num = self.inv_num_input.text().strip()
        date = self.date_input.date().toString('yyyy-MM-dd')
        supplier = self.supplier_combo.currentText().strip()
        if not inv_num or not supplier or self.table.rowCount() == 0:
            QMessageBox.warning(self, 'خطأ', 'يرجى إدخال جميع البيانات' if LANGUAGE == 'ar' else 'Please fill all fields')
            return
        conn = sqlite3.connect('db/warehouse.db')
        c = conn.cursor()
        # إضافة المورد إذا لم يكن موجود
        c.execute('SELECT id FROM suppliers WHERE name=?', (supplier,))
        supplier_row = c.fetchone()
        if supplier_row:
            supplier_id = supplier_row[0]
        else:
            c.execute('INSERT INTO suppliers (name) VALUES (?)', (supplier,))
            supplier_id = c.lastrowid
        # إضافة الفاتورة
        c.execute('INSERT INTO purchase_invoices (invoice_number, date, supplier_id, total) VALUES (?, ?, ?, ?)',
                  (inv_num, date, supplier_id, 0))
        invoice_id = c.lastrowid
        total = 0
        for row in range(self.table.rowCount()):
            code = self.table.item(row, 0).text().strip()
            name = self.table.item(row, 1).text().strip()
            qty = int(self.table.item(row, 2).text().strip() or '0')
            price = float(self.table.item(row, 3).text().strip() or '0')
            item_total = qty * price
            total += item_total
            # جلب معرف المنتج
            c.execute('SELECT id FROM products WHERE code=?', (code,))
            prod_row = c.fetchone()
            if prod_row:
                prod_id = prod_row[0]
                # تحديث الكمية
                c.execute('UPDATE products SET quantity = quantity + ? WHERE id=?', (qty, prod_id))
            else:
                # إضافة منتج جديد
                c.execute('INSERT INTO products (code, name, unit, quantity) VALUES (?, ?, ?, ?)', (code, name, '', qty))
                prod_id = c.lastrowid
            # إضافة تفاصيل الفاتورة
            c.execute('''INSERT INTO purchase_invoice_items (invoice_id, product_id, quantity, price, total) VALUES (?, ?, ?, ?, ?)''',
                      (invoice_id, prod_id, qty, price, item_total))
        # تحديث الإجمالي
        c.execute('UPDATE purchase_invoices SET total=? WHERE id=?', (total, invoice_id))
        conn.commit()
        conn.close()
        QMessageBox.information(self, 'تم', 'تم حفظ الفاتورة بنجاح' if LANGUAGE == 'ar' else 'Invoice saved successfully')
        self.clear_all()

    def clear_all(self):
        self.inv_num_input.clear()
        self.supplier_combo.setCurrentIndex(-1)
        self.table.setRowCount(0)
        self.date_input.setDate(QDate.currentDate())
