from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBox<PERSON>ayout, QLabel, QPushButton, QComboBox, QDateEdit, QTableWidget, QTableWidgetItem, QFileDialog
from PyQt6.QtCore import Qt, QDate
from config import LANGUAGE
import sqlite3
import pandas as pd
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas

class ReportsManager(QWidget):
    def __init__(self):
        super().__init__()
        layout = QVBoxLayout()
        # خيارات التقرير
        options_layout = QHBoxLayout()
        self.report_type = QComboBox()
        self.report_type.addItems([
            'فواتير الإدخال' if LANGUAGE == 'ar' else 'Purchase Invoices',
            'فواتير الإخراج' if LANGUAGE == 'ar' else 'Sales Invoices',
            'المخزون الحالي' if LANGUAGE == 'ar' else 'Current Stock',
        ])
        self.from_date = QDateEdit()
        self.from_date.setDate(QDate.currentDate())
        self.to_date = QDateEdit()
        self.to_date.setDate(QDate.currentDate())
        self.filter_combo = QComboBox()
        self.filter_combo.setEditable(True)
        self.filter_combo.setPlaceholderText('المورد/العميل')
        self.show_btn = QPushButton('عرض التقرير' if LANGUAGE == 'ar' else 'Show Report')
        self.export_pdf_btn = QPushButton('تصدير PDF')
        self.export_excel_btn = QPushButton('تصدير Excel')
        self.show_btn.clicked.connect(self.show_report)
        self.export_pdf_btn.clicked.connect(self.export_pdf)
        self.export_excel_btn.clicked.connect(self.export_excel)
        options_layout.addWidget(self.report_type)
        options_layout.addWidget(self.from_date)
        options_layout.addWidget(self.to_date)
        options_layout.addWidget(self.filter_combo)
        options_layout.addWidget(self.show_btn)
        options_layout.addWidget(self.export_pdf_btn)
        options_layout.addWidget(self.export_excel_btn)
        layout.addLayout(options_layout)
        # جدول النتائج
        self.table = QTableWidget()
        layout.addWidget(self.table)
        self.setLayout(layout)
        self.data = []
        self.headers = []
        self.report_type.currentIndexChanged.connect(self.update_filter_combo)
        self.update_filter_combo()

    def update_filter_combo(self):
        self.filter_combo.clear()
        conn = sqlite3.connect('db/warehouse.db')
        c = conn.cursor()
        if self.report_type.currentIndex() == 0:
            c.execute('SELECT name FROM suppliers')
        elif self.report_type.currentIndex() == 1:
            c.execute('SELECT name FROM customers')
        else:
            self.filter_combo.setEnabled(False)
            conn.close()
            return
        self.filter_combo.setEnabled(True)
        self.filter_combo.addItem('')
        self.filter_combo.addItems([row[0] for row in c.fetchall()])
        conn.close()

    def show_report(self):
        conn = sqlite3.connect('db/warehouse.db')
        c = conn.cursor()
        from_date = self.from_date.date().toString('yyyy-MM-dd')
        to_date = self.to_date.date().toString('yyyy-MM-dd')
        filter_val = self.filter_combo.currentText().strip()
        if self.report_type.currentIndex() == 0:
            # فواتير الإدخال
            query = '''SELECT p.invoice_number, p.date, s.name, i.quantity, i.price, i.total FROM purchase_invoices p
                      JOIN suppliers s ON p.supplier_id = s.id
                      JOIN purchase_invoice_items i ON i.invoice_id = p.id
                      WHERE p.date BETWEEN ? AND ?'''
            params = [from_date, to_date]
            if filter_val:
                query += ' AND s.name=?'
                params.append(filter_val)
            c.execute(query, params)
            self.headers = ['رقم الفاتورة', 'التاريخ', 'المورد', 'الكمية', 'السعر', 'الإجمالي'] if LANGUAGE == 'ar' else ['Invoice No', 'Date', 'Supplier', 'Qty', 'Price', 'Total']
        elif self.report_type.currentIndex() == 1:
            # فواتير الإخراج
            query = '''SELECT s.invoice_number, s.date, c.name, i.quantity, i.price, i.total FROM sales_invoices s
                      JOIN customers c ON s.customer_id = c.id
                      JOIN sales_invoice_items i ON i.invoice_id = s.id
                      WHERE s.date BETWEEN ? AND ?'''
            params = [from_date, to_date]
            if filter_val:
                query += ' AND c.name=?'
                params.append(filter_val)
            c.execute(query, params)
            self.headers = ['رقم الفاتورة', 'التاريخ', 'العميل', 'الكمية', 'السعر', 'الإجمالي'] if LANGUAGE == 'ar' else ['Invoice No', 'Date', 'Customer', 'Qty', 'Price', 'Total']
        else:
            # المخزون الحالي
            c.execute('SELECT code, name, unit, quantity, status, category FROM products')
            self.headers = ['كود المادة', 'اسم المادة', 'الوحدة', 'الكمية', 'الحالة', 'التصنيف'] if LANGUAGE == 'ar' else ['Code', 'Name', 'Unit', 'Qty', 'Status', 'Category']
        self.data = c.fetchall()
        conn.close()
        self.table.setColumnCount(len(self.headers))
        self.table.setHorizontalHeaderLabels(self.headers)
        self.table.setRowCount(0)
        for row_data in self.data:
            row = self.table.rowCount()
            self.table.insertRow(row)
            for col, value in enumerate(row_data):
                self.table.setItem(row, col, QTableWidgetItem(str(value)))

    def export_pdf(self):
        if not self.data:
            return
        file, _ = QFileDialog.getSaveFileName(self, 'تصدير PDF', '', 'PDF Files (*.pdf)')
        if not file:
            return
        c = canvas.Canvas(file, pagesize=A4)
        width, height = A4
        c.setFont('Helvetica', 10)
        y = height - 40
        for i, header in enumerate(self.headers):
            c.drawString(40 + i*80, y, str(header))
        y -= 20
        for row in self.data:
            for i, value in enumerate(row):
                c.drawString(40 + i*80, y, str(value))
            y -= 20
            if y < 40:
                c.showPage()
                y = height - 40
        c.save()

    def export_excel(self):
        if not self.data:
            return
        file, _ = QFileDialog.getSaveFileName(self, 'تصدير Excel', '', 'Excel Files (*.xlsx)')
        if not file:
            return
        df = pd.DataFrame(self.data, columns=self.headers)
        df.to_excel(file, index=False)
